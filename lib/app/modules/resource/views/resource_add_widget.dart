import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/mother_tongue_resp/mother_tongue_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/image.dart';
import 'package:lsenglish_admin/util/oss.dart';
import 'package:lsenglish_admin/util/toast.dart';

import '../../../../models/resource_resp/resource_resp.dart';
import '../../../../models/series_resp/series_resp.dart';

class ResourceAddReq {
  String? id;
  List<ResourceRelationReq>? resourceRelations;
  ResourceRelationReq? originResourceRelation;
  String? videoUrl;
  String? cover;
  List<String>? categoryIds;
  List<String>? seriesIds;
  int? duration;
  String? publishedAt;
  String? author;
  List<String>? tags;

  ResourceAddReq({
    this.id,
    this.resourceRelations,
    this.originResourceRelation,
    this.videoUrl,
    this.cover,
    this.categoryIds,
    this.duration,
    this.publishedAt,
    this.author,
    this.seriesIds,
    this.tags,
  });
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'resourceRelations': resourceRelations?.map((title) => title.toMap()).toList(),
      'originResourceRelation': originResourceRelation?.toMap(),
      'videoUrl': videoUrl,
      'cover': cover,
      'categoryIds': categoryIds,
      'duration': duration,
      'publishedAt': publishedAt,
      'author': author,
      'seriesIds': seriesIds,
      'tags': tags,
    };
  }
}

class ResourceRelationReq {
  String? langCode;
  String? title;
  String? description;
  String? subtitleUrl;

  ResourceRelationReq({
    this.langCode,
    this.title,
    this.description,
    this.subtitleUrl,
  });
  Map<String, dynamic> toMap() {
    return {
      'langCode': langCode,
      'title': title,
      'description': description,
      'subtitleUrl': subtitleUrl,
    };
  }
}

class ResourceAddWidget extends StatefulWidget {
  final ResourceResp? resourceResp;
  final VoidCallback addSuccessCallback;
  const ResourceAddWidget({super.key, this.resourceResp, required this.addSuccessCallback});

  @override
  State<ResourceAddWidget> createState() => _CategoryAddWidgetState();
}

class _CategoryAddWidgetState extends State<ResourceAddWidget> {
  PlatformFile? selectVideoFile;
  PlatformFile? selectImageFile;
  List<SeriesResp> serieses = [];
  List<String> selectSeriesIds = [];
  List<CategoryResp> categories = [];
  List<String> selectCategoryIds = [];
  List<String> resourceTags = [];
  List<NativeLangResp> langList = [];
  Map<String, TextEditingController> _titleControllers = {};
  Map<String, TextEditingController> _descControllers = {};
  Map<String, TextEditingController> _subtitleControllers = {};
  String videoRemoteUrl = "";
  String imageRemoteUrl = "";
  final TextEditingController _controllerImage = TextEditingController();
  final TextEditingController _controllerVideo = TextEditingController();
  final TextEditingController _controllerDuration = TextEditingController();
  final TextEditingController _controllerTag = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.resourceResp != null) {
      Net.getRestClient().getResourceDetail(widget.resourceResp?.id ?? "").then((onValue) {
        var resourceResp = onValue.data;
        setState(() {
          var videoDuration = Duration(seconds: resourceResp.duration ?? 0);
          videoRemoteUrl = resourceResp.videoUrl!;
          imageRemoteUrl = resourceResp.cover!;
          selectCategoryIds = resourceResp.categories!.map((toElement) => toElement.id ?? "").toList();
          selectSeriesIds = resourceResp.serieses!.map((toElement) => toElement.id ?? "").toList();
          resourceTags = resourceResp.tags ?? [];
          for (var rel in resourceResp.resourceRelations ?? []) {
            _titleControllers[rel.langCode ?? '']?.text = rel.title ?? '';
            _descControllers[rel.langCode ?? '']?.text = rel.description ?? '';
            _subtitleControllers[rel.langCode ?? '']?.text = rel.subtitleUrl ?? '';
          }
          if (resourceResp.originResourceRelation != null) {
            var rel = resourceResp.originResourceRelation!;
            _titleControllers[rel.langCode ?? '']?.text = rel.title ?? '';
            _descControllers[rel.langCode ?? '']?.text = rel.description ?? '';
            _subtitleControllers[rel.langCode ?? '']?.text = rel.subtitleUrl ?? '';
          }
          _controllerImage.text = imageRemoteUrl;
          _controllerVideo.text = videoRemoteUrl;
          _controllerDuration.text = videoDuration.inSeconds.toString();
        });
      });
    }

    Net.getRestClient().categoryList(1, 100).then((onValue) {
      categories.clear();
      setState(() {
        categories.addAll(onValue.data.data);
      });
    });
    Net.getRestClient().seriesList(0, 0).then((onValue) {
      serieses.clear();
      setState(() {
        serieses.addAll(onValue.data.data);
      });
    });
    Net.getRestClient().nativeLangList().then((onValue) {
      setState(() {
        langList.clear();
        langList.addAll(onValue.data);
        for (var lang in langList) {
          _titleControllers[lang.code ?? ''] = TextEditingController();
          _descControllers[lang.code ?? ''] = TextEditingController();
          _subtitleControllers[lang.code ?? ''] = TextEditingController();
        }
      });
    });
  }

  @override
  void dispose() {
    _controllerImage.dispose();
    _controllerVideo.dispose();
    _controllerDuration.dispose();
    _controllerTag.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resourceResp == null ? "添加资源" : "编辑资源"),
      content: SizedBox(
        width: 800,
        height: 700,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                        Text('选择剧集(可选)', style: Get.textTheme.titleMedium),
                        const Gap(20),
                        Wrap(
                          spacing: 8.0,
                          runSpacing: 4.0,
                          children: serieses
                              .map((series) => GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        if (selectSeriesIds.contains(series.id)) {
                                          selectSeriesIds.remove(series.id ?? "");
                                        } else {
                                          selectSeriesIds.add(series.id ?? "");
                                        }
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                                      decoration: BoxDecoration(
                                          color: selectSeriesIds.contains(series.id) ? Get.theme.primaryColor : Colors.grey,
                                          borderRadius: BorderRadius.circular(16)),
                                      child: Text(
                                        (() {
                                          if (series.seriesRelations != null && series.seriesRelations!.isNotEmpty) {
                                            var zh = series.seriesRelations!.firstWhere(
                                              (e) => e.langCode == 'zh-CN',
                                              orElse: () => series.seriesRelations!.first,
                                            );
                                            return zh.title ?? '--';
                                          }
                                          return '--';
                                        })(),
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                    ),
                                  ))
                              .toList(),
                        ),
                        const Gap(20),
                        Text('选择分类', style: Get.textTheme.titleMedium),
                        const Gap(20),
                        Wrap(
                          spacing: 8.0,
                          runSpacing: 4.0,
                          children: categories
                              .map((category) => GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        if (selectCategoryIds.contains(category.id)) {
                                          selectCategoryIds.remove(category.id ?? "");
                                        } else {
                                          selectCategoryIds.add(category.id ?? "");
                                        }
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                                      decoration: BoxDecoration(
                                          color: selectCategoryIds.contains(category.id) ? Get.theme.primaryColor : Colors.grey,
                                          borderRadius: BorderRadius.circular(16)),
                                      child: Text(
                                        category.name ?? "",
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                    ),
                                  ))
                              .toList(),
                        ),
                        const Gap(20),
                        Text('添加字幕', style: Get.textTheme.titleMedium),
                        const Gap(20),
                        ...langList.map((lang) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${lang.name}（${lang.code}）标题', style: Get.textTheme.titleSmall),
                            TextField(
                              controller: _titleControllers[lang.code ?? ''],
                              decoration: InputDecoration(labelText: '${lang.name}标题', filled: true),
                            ),
                            const Gap(10),
                            Text('${lang.name}（${lang.code}）描述', style: Get.textTheme.titleSmall),
                            TextField(
                              controller: _descControllers[lang.code ?? ''],
                              decoration: InputDecoration(labelText: '${lang.name}描述', filled: true),
                            ),
                            const Gap(10),
                            Text('${lang.name}（${lang.code}）字幕地址', style: Get.textTheme.titleSmall),
                            TextField(
                              controller: _subtitleControllers[lang.code ?? ''],
                              decoration: InputDecoration(labelText: '${lang.name}字幕地址', filled: true),
                            ),
                            const Gap(20),
                          ],
                        )),
                        const Gap(20),
                        Text('添加视频', style: Get.textTheme.titleMedium),
                        ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 300),
                          child: TextField(
                            controller: _controllerVideo,
                            onChanged: (value) {
                              setState(() {
                                videoRemoteUrl = value;
                                selectVideoFile = null;
                              });
                            },
                            decoration: const InputDecoration(
                              labelText: '输入视频地址或者上传文件',
                              filled: true,
                            ),
                          ),
                        ),
                        ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 300),
                          child: TextField(
                            controller: _controllerDuration,
                            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                            decoration: const InputDecoration(
                              labelText: '输入视频时长(秒数)',
                              filled: true,
                            ),
                          ),
                        ),
                        Visibility(visible: selectVideoFile != null, child: Text("选择的文件: ${selectVideoFile?.name}")),
                        const Gap(20),
                        FilledButton(
                          onPressed: () async {
                            FilePickerResult? result = await FilePicker.platform.pickFiles();
                            if (result != null) {
                              setState(() {
                                selectVideoFile = result.files.first;
                                videoRemoteUrl = "";
                                _controllerVideo.text = "";
                              });
                            }
                          },
                          child: const Text("选择视频文件"),
                        ),
                        const Gap(20),
                        Text('添加标签', style: Get.textTheme.titleMedium),
                        const Gap(10),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: resourceTags.map((tag) {
                            return Chip(
                              label: Text(tag),
                              deleteIcon: const Icon(Icons.close, size: 18),
                              onDeleted: () {
                                setState(() {
                                  resourceTags.remove(tag);
                                });
                              },
                            );
                          }).toList(),
                        ),
                        const Gap(10),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _controllerTag,
                                decoration: const InputDecoration(
                                  labelText: '输入标签',
                                  hintText: '输入标签后点击添加',
                                  filled: true,
                                ),
                                onSubmitted: (value) {
                                  if (value.trim().isNotEmpty) {
                                    setState(() {
                                      if (!resourceTags.contains(value.trim())) {
                                        resourceTags.add(value.trim());
                                      }
                                      _controllerTag.clear();
                                    });
                                  }
                                },
                              ),
                            ),
                            const Gap(10),
                            FilledButton(
                              onPressed: () {
                                final value = _controllerTag.text;
                                if (value.trim().isNotEmpty) {
                                  setState(() {
                                    if (!resourceTags.contains(value.trim())) {
                                      resourceTags.add(value.trim());
                                    }
                                    _controllerTag.clear();
                                  });
                                }
                              },
                              child: const Text("添加标签"),
                            ),
                          ],
                        ),
                        const Gap(20),
                        Text('添加封面', style: Get.textTheme.titleMedium),
                        ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 300),
                          child: TextField(
                            controller: _controllerImage,
                            onChanged: (value) {
                              setState(() {
                                imageRemoteUrl = value;
                                selectImageFile = null;
                              });
                            },
                            decoration: const InputDecoration(
                              labelText: '输入图片地址或者上传文件',
                              filled: true,
                            ),
                          ),
                        ),
                        Visibility(visible: selectImageFile != null, child: Text("选择的文件: ${selectImageFile?.name}")),
                        Visibility(visible: imageRemoteUrl != "", child: ImageLoader(imageRemoteUrl, size: 160)),
                        const Gap(20),
                        FilledButton(
                          onPressed: () async {
                            FilePickerResult? result = await FilePicker.platform.pickFiles();
                            if (result != null) {
                              setState(() {
                                selectImageFile = result.files.first;
                                imageRemoteUrl = "";
                                _controllerImage.text = "";
                              });
                            }
                          },
                          child: const Text("选择封面"),
                        ),
                      ],
                    ),
                  )),

                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (selectCategoryIds.isEmpty && selectSeriesIds.isEmpty) {
      Get.snackbar("错误", "请选择至少一个资源分类");
      return false;
    }

    // 多语言校验：只要有一个字段有数据，则该语言所有字段都必须填写
    bool hasValidLang = false;
    for (var lang in langList) {
      final title = _titleControllers[lang.code ?? '']?.text ?? '';
      final desc = _descControllers[lang.code ?? '']?.text ?? '';
      final subtitle = _subtitleControllers[lang.code ?? '']?.text ?? '';
      final anyFilled = title.isNotEmpty || desc.isNotEmpty || subtitle.isNotEmpty;
      final allFilled = title.isNotEmpty && desc.isNotEmpty && subtitle.isNotEmpty;
      if (anyFilled && !allFilled) {
        Get.snackbar("错误", "${lang.name}（${lang.code}）的标题、描述、字幕都不能为空");
        return false;
      }
      if (allFilled) {
        hasValidLang = true;
      }
    }
    if (!hasValidLang) {
      Get.snackbar("错误", "请至少填写一个完整的多语言信息（标题、描述、字幕）");
      return false;
    }
    if (videoRemoteUrl.isEmpty && selectVideoFile == null) {
      Get.snackbar("错误", "请上传视频");
      return false;
    }
    if (_controllerDuration.text.isEmpty) {
      Get.snackbar("错误", "视频时长为空,请上传视频或者主动输入");
      return false;
    }
    if (imageRemoteUrl.isEmpty && selectImageFile == null) {
      Get.snackbar("错误", "请上传封面");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    try {
      if (selectVideoFile != null) {
        videoRemoteUrl = await OssUtil().uploadVideoFile(
          fileBytes: selectVideoFile!.bytes,
          fileName: selectVideoFile!.name
        ) ?? "";
      }
      if (selectImageFile != null) {
        imageRemoteUrl = await OssUtil().uploadSystemResourceImageFile(
          fileBytes: selectImageFile!.bytes,
          fileName: selectImageFile!.name
        ) ?? "";
      }

      if (videoRemoteUrl.isEmpty) {
        Get.snackbar("错误", "视频传输错误");
        return;
      }
      if (imageRemoteUrl.isEmpty) {
        Get.snackbar("错误", "封面传输错误");
        return;
      }

      List<ResourceRelationReq> resourceRelations = [];
      ResourceRelationReq? originResourceRelation;
      for (var lang in langList) {
        final title = _titleControllers[lang.code ?? '']?.text;
        final desc = _descControllers[lang.code ?? '']?.text;
        final subtitle = _subtitleControllers[lang.code ?? '']?.text;
        if ((title?.isNotEmpty ?? false) && (desc?.isNotEmpty ?? false) && (subtitle?.isNotEmpty ?? false)) {
          var rel = ResourceRelationReq(
            langCode: lang.code,
            title: title,
            description: desc,
            subtitleUrl: subtitle,
          );
          if (lang.code == 'zh-CN') {
            originResourceRelation = rel;
          } else {
            resourceRelations.add(rel);
          }
        }
      }

      ResourceAddReq tempResourceResp = ResourceAddReq(
        id: widget.resourceResp?.id ?? "",
        resourceRelations: resourceRelations,
        originResourceRelation: originResourceRelation,
        cover: imageRemoteUrl,
        videoUrl: videoRemoteUrl,
        categoryIds: selectCategoryIds,
        seriesIds: selectSeriesIds,
        duration: int.parse(_controllerDuration.text),
        publishedAt: (widget.resourceResp?.publishedAt == null || widget.resourceResp?.publishedAt == "")
            ? DateTime.now().toIso8601String()
            : widget.resourceResp?.publishedAt,
        author: widget.resourceResp?.author ?? "LS100",
        tags: resourceTags,
      );

      await Net.getRestClient().addResource(tempResourceResp.toMap());
      widget.addSuccessCallback();
      Get.back();
      Get.snackbar("成功", widget.resourceResp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      OssUtil().deleteOssFile(imageRemoteUrl);
      OssUtil().deleteOssFile(videoRemoteUrl);
      Get.snackbar("错误", "添加出错${e.toString()}");
    }
  }
}
