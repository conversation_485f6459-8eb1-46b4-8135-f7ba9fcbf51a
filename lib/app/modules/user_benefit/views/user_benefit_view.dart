import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';

import '../controllers/user_benefit_controller.dart';

class UserBenefitView extends BasePageListView<UserBenefitResp, UserBenefitController> {
  const UserBenefitView({Key? key}) : super(key: key);

  @override
  Widget buildSearchWidget() {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: "用户ID",
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                  onChanged: (value) => controller.uidFilter.value = value,
                ),
              ),
              const Gap(16),
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: "权益组编码",
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.group_work),
                  ),
                  onChanged: (value) => controller.benefitGroupCodeFilter.value = value,
                ),
              ),
              const Gap(16),
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: "权益编码",
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.card_giftcard),
                  ),
                  onChanged: (value) => controller.benefitCodeFilter.value = value,
                ),
              ),
              const Gap(16),
              Expanded(
                child: Obx(() => DropdownButtonFormField<int>(
                  decoration: const InputDecoration(
                    labelText: "状态",
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.toggle_on),
                  ),
                  value: controller.statusFilter.value,
                  items: const [
                    DropdownMenuItem(value: -1, child: Text("全部")),
                    DropdownMenuItem(value: 0, child: Text("失效")),
                    DropdownMenuItem(value: 1, child: Text("启用")),
                  ],
                  onChanged: (value) => controller.statusFilter.value = value ?? -1,
                )),
              ),
            ],
          ),
          const Gap(16),
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: controller.searchUserBenefits,
                icon: const Icon(Icons.search),
                label: const Text("搜索"),
              ),
              const Gap(8),
              OutlinedButton.icon(
                onPressed: controller.resetSearch,
                icon: const Icon(Icons.refresh),
                label: const Text("重置"),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID"), fixedWidth: 80),
      DataColumn2(label: Text("用户ID"), fixedWidth: 120),
      DataColumn2(label: Text("权益名称")),
      DataColumn2(label: Text("权益编码"), fixedWidth: 120),
      DataColumn2(label: Text("权益组编码"), fixedWidth: 120),
      DataColumn2(label: Text("使用情况"), fixedWidth: 100),
      DataColumn2(label: Text("状态"), fixedWidth: 80),
      DataColumn2(label: Text("来源"), fixedWidth: 100),
      DataColumn2(label: Text("下次刷新"), fixedWidth: 150),
      DataColumn2(label: Text("操作"), fixedWidth: 150),
    ];
  }

  @override
  void onAddPressed() async {
    // 用户权益只能查看，不能添加
    Get.snackbar("提示", "用户权益只能查看，不能添加");
  }

  @override
  String getTitle() {
    return "用户权益管理";
  }

  @override
  List<DataCell> buildDataCells(UserBenefitResp item, int index) {
    return [
      DataCell(Text(item.id.toString())),
      DataCell(Text(item.uid ?? "--")),
      DataCell(Text(item.benefitName ?? "--")),
      DataCell(Text(item.benefitCode ?? "--")),
      DataCell(Text(item.benefitGroupCode ?? "--")),
      DataCell(
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(item.usageProgressText),
            const Gap(4),
            LinearProgressIndicator(
              value: item.usageProgress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                item.usageProgress > 0.8 ? Colors.red : Colors.green,
              ),
            ),
          ],
        ),
      ),
      DataCell(
        Switch(
          value: item.status == 1,
          onChanged: (value) {
            controller.toggleStatus(item);
          },
        ),
      ),
      DataCell(Text(item.source ?? "--")),
      DataCell(Text(item.nextRefreshTime ?? "--")),
      DataCell(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue),
              tooltip: "查看详情",
              onPressed: () {
                // 显示用户权益详情对话框
                _showUserBenefitDetail(item);
              },
            ),
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId(UserBenefitResp item, int index) {
    return item.id?.toString();
  }

  @override
  DataRow2 buildDataRow(UserBenefitResp item, int index) {
    return DataRow2(
      specificRowHeight: getSpecificRowHeight(),
      onDoubleTap: () {
        // 用户权益只能查看详情，不能编辑
        _showUserBenefitDetail(item);
      },
      key: ValueKey<String?>(getModelId(item, index)),
      // 禁用选中功能 - 不设置 onSelectChanged
      cells: buildDataCells(item, index),
    );
  }

  // 显示用户权益详情
  void _showUserBenefitDetail(UserBenefitResp item) {
    Get.dialog(
      AlertDialog(
        title: const Text("用户权益详情"),
        content: SizedBox(
          width: 500,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow("用户ID", item.uid ?? "--"),
              _buildDetailRow("权益名称", item.benefitName ?? "--"),
              _buildDetailRow("权益编码", item.benefitCode ?? "--"),
              _buildDetailRow("权益组编码", item.benefitGroupCode ?? "--"),
              _buildDetailRow("剩余数量", item.remain?.toString() ?? "--"),
              _buildDetailRow("总数量", item.total?.toString() ?? "--"),
              _buildDetailRow("使用进度", item.usageProgressText),
              _buildDetailRow("状态", item.statusText),
              _buildDetailRow("来源", item.source ?? "--"),
              _buildDetailRow("最后刷新时间", item.lastRefreshTime ?? "--"),
              _buildDetailRow("下次刷新时间", item.nextRefreshTime ?? "--"),
              _buildDetailRow("创建时间", item.createdAt ?? "--"),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("关闭"),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              "$label:",
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
