import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import '../controllers/category_controller.dart';

class CategoryAddWidget extends StatefulWidget {
  final CategoryResp? resp;
  const CategoryAddWidget({super.key, this.resp});

  @override
  State<CategoryAddWidget> createState() => _CategoryAddWidgetState();
}

class _CategoryAddWidgetState extends State<CategoryAddWidget> {
  final TextEditingController _controllerName = TextEditingController();
  final TextEditingController _controllerDesc = TextEditingController();
  List<String> categoryTypes = ["等级", "场景", "主题"];
  List<String> categoryTypesIds = ["1", "2", "3"];
  var selectCategoryTypesIndex = 0;

  @override
  void initState() {
    super.initState();
    _controllerName.text = widget.resp?.name ?? "";
    _controllerDesc.text = widget.resp?.description ?? "";
    selectCategoryTypesIndex = categoryTypesIds.indexOf(widget.resp?.categoryTypeId ?? "1");
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Card(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "添加分类",
                  style: Get.textTheme.titleLarge,
                ),
                const Gap(20),
                Text('选择分类的类型', style: Get.textTheme.titleMedium),
                const Gap(20),
                Wrap(
                  spacing: 8.0,
                  runSpacing: 4.0,
                  children: categoryTypes.asMap().entries.map((entry) {
                    int index = entry.key;
                    String types = entry.value;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          selectCategoryTypesIndex = index;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                        decoration: BoxDecoration(
                            color: selectCategoryTypesIndex == index ? Get.theme.primaryColor : Colors.grey, borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          types,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    );
                  }).toList(),
                ),
                const Gap(20),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controllerName,
                    decoration: const InputDecoration(
                      labelText: '分类名称',
                      filled: true,
                    ),
                  ),
                ),
                const Gap(20),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controllerDesc,
                    decoration: const InputDecoration(
                      labelText: '分类描述',
                      filled: true,
                    ),
                  ),
                ),
                const Gap(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FilledButton(
                      onPressed: () {
                        _add();
                        Get.back();
                      },
                      child: Text(widget.resp?.id == null ? "添加" : "更新"),
                    ),
                    const Gap(10),
                    FilledButton(
                      onPressed: () {
                        Get.back();
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                        foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                      ),
                      child: const Text("取消"),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _add() {
    CategoryResp tempResourceResp;
    if (widget.resp == null) {
      tempResourceResp = CategoryResp();
    } else {
      tempResourceResp = widget.resp!;
    }
    tempResourceResp.name = _controllerName.text;
    tempResourceResp.description = _controllerDesc.text;
    tempResourceResp.categoryTypeId = categoryTypesIds[selectCategoryTypesIndex];
    Get.find<CategoryController>().addEntity(tempResourceResp.toJson());
  }
}
