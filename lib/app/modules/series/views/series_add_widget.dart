import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/series/controllers/series_controller.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/series_resp/series_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/image.dart';
import 'package:lsenglish_admin/util/oss.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/dialog_wrap.dart';
import 'package:lsenglish_admin/models/mother_tongue_resp/mother_tongue_resp.dart';

class SeriesAddWidget extends StatefulWidget {
  final SeriesResp? resp;
  const SeriesAddWidget({super.key, this.resp});

  @override
  State<SeriesAddWidget> createState() => _SeriesAddWidgetState();
}

class _SeriesAddWidgetState extends State<SeriesAddWidget> {
  final TextEditingController _controllerImage = TextEditingController();
  List<String> selectCategoryIds = [];
  List<List<CategoryResp>> categories = [];
  List<String> categoryTypes = ["等级", "主题", "场景"];
  PlatformFile? selectImageFile;
  String imageRemoteUrl = "";

  List<NativeLangResp> langList = [];
  Map<String, TextEditingController> _titleControllers = {};
  Map<String, TextEditingController> _descControllers = {};
  Map<String, TextEditingController> _statementControllers = {};

  @override
  void initState() {
    super.initState();
    Net.getRestClient().nativeLangList().then((onValue) {
      setState(() {
        langList.clear();
        langList.addAll(onValue.data);
        // 初始化多语言输入框
        for (var lang in langList) {
          _titleControllers[lang.code ?? ''] = TextEditingController();
          _descControllers[lang.code ?? ''] = TextEditingController();
          _statementControllers[lang.code ?? ''] = TextEditingController();
        }
        // 回填编辑数据
        if (widget.resp?.seriesRelations != null) {
          for (var rel in widget.resp!.seriesRelations!) {
            _titleControllers[rel.langCode ?? '']?.text = rel.title ?? '';
            _descControllers[rel.langCode ?? '']?.text = rel.description ?? '';
            _statementControllers[rel.langCode ?? '']?.text = rel.statement ?? '';
          }
        }
      });
    });
    imageRemoteUrl = widget.resp?.cover ?? "";
    _controllerImage.text = imageRemoteUrl;
    if (widget.resp != null) {
      Net.getRestClient().getSeriesDetail(widget.resp!.id ?? "").then((onValue) {
        setState(() {
          selectCategoryIds = onValue.data.categoryIds ?? [];
        });
      });
    }
    Net.getRestClient().categoryList(1, 100).then((onValue) {
      categories.clear();
      setState(() {
        var levelCategories = onValue.data.data.where((category) => category.categoryTypeId == "1").toList();
        var topicCategories = onValue.data.data.where((category) => category.categoryTypeId == "2").toList();
        var senceCategories = onValue.data.data.where((category) => category.categoryTypeId == "3").toList();
        categories.add(levelCategories);
        categories.add(topicCategories);
        categories.add(senceCategories);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加剧集" : "编辑剧集"),
      content: SizedBox(
        width: 700,
        height: 600,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                Wrap(
                  direction: Axis.vertical,
                  children: categories.asMap().entries.map((entry) {
                    int index = entry.key;
                    List<CategoryResp> categoriesItem = entry.value;
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(8),
                        Text(categoryTypes[index], style: Get.textTheme.titleMedium),
                        const Gap(8),
                        Wrap(
                          spacing: 8.0,
                          runSpacing: 4.0,
                          children: categoriesItem
                              .map((category) => GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        if (selectCategoryIds.contains(category.id)) {
                                          selectCategoryIds.remove(category.id ?? "");
                                        } else {
                                          selectCategoryIds.add(category.id ?? "");
                                        }
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                                      decoration: BoxDecoration(
                                          color: selectCategoryIds.contains(category.id) ? Get.theme.primaryColor : Colors.grey,
                                          borderRadius: BorderRadius.circular(16)),
                                      child: Text(
                                        category.name ?? "",
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                    ),
                                  ))
                              .toList(),
                        )
                      ],
                    );
                  }).toList(),
                ),
                const Gap(20),
                ...langList.map((lang) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${lang.name}（${lang.code}）标题', style: Get.textTheme.titleSmall),
                        TextField(
                          controller: _titleControllers[lang.code ?? ''],
                          decoration: InputDecoration(labelText: '${lang.name}标题', filled: true),
                        ),
                        const Gap(10),
                        Text('${lang.name}（${lang.code}）描述', style: Get.textTheme.titleSmall),
                        TextField(
                          controller: _descControllers[lang.code ?? ''],
                          decoration: InputDecoration(labelText: '${lang.name}描述', filled: true),
                        ),
                        const Gap(10),
                        Text('${lang.name}（${lang.code}）声明', style: Get.textTheme.titleSmall),
                        TextField(
                          controller: _statementControllers[lang.code ?? ''],
                          decoration: InputDecoration(labelText: '${lang.name}声明(可选)', filled: true),
                        ),
                        const Gap(20),
                      ],
                    )),
                const Gap(20),
                Text('添加封面', style: Get.textTheme.titleMedium),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controllerImage,
                    onChanged: (value) {
                      setState(() {
                        imageRemoteUrl = value;
                        selectImageFile = null;
                      });
                    },
                    decoration: const InputDecoration(
                      labelText: '输入图片地址或者上传文件',
                      filled: true,
                    ),
                  ),
                ),
                Visibility(visible: selectImageFile != null, child: Text("选择的文件: ${selectImageFile?.name}")),
                Visibility(visible: imageRemoteUrl != "", child: ImageLoader(imageRemoteUrl, size: 160)),
                const Gap(20),
                FilledButton(
                  onPressed: () async {
                    FilePickerResult? result = await FilePicker.platform.pickFiles();
                    if (result != null) {
                      OssUtil().deleteOssFile(imageRemoteUrl);
                      setState(() {
                        selectImageFile = result.files.first;
                        imageRemoteUrl = "";
                        _controllerImage.text = "";
                      });
                    }
                  },
                  child: const Text("选择封面"),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (selectCategoryIds.isEmpty) {
      Get.snackbar("错误", "请选择至少一个资源分类");
      return false;
    }

    // 多语言校验：只要有一个字段有数据，则该语言所有字段都必须填写
    bool hasValidLang = false;
    for (var lang in langList) {
      final title = _titleControllers[lang.code ?? '']?.text ?? '';
      final desc = _descControllers[lang.code ?? '']?.text ?? '';
      final statement = _statementControllers[lang.code ?? '']?.text ?? '';
      final anyFilled = title.isNotEmpty || desc.isNotEmpty || statement.isNotEmpty;
      final allFilled = title.isNotEmpty && desc.isNotEmpty && statement.isNotEmpty;
      if (anyFilled && !allFilled) {
        Get.snackbar("错误", "${lang.name}（${lang.code}）的标题、描述、声明都不能为空");
        return false;
      }
      if (allFilled) {
        hasValidLang = true;
      }
    }
    if (!hasValidLang) {
      Get.snackbar("错误", "请至少填写一个完整的多语言信息（标题、描述、声明）");
      return false;
    }
    if (imageRemoteUrl.isEmpty && selectImageFile == null) {
      Get.snackbar("错误", "请上传封面");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    try {
      if (selectImageFile != null && imageRemoteUrl.isEmpty) {
        imageRemoteUrl = await OssUtil().uploadSystemResourceImageFile(
          fileBytes: selectImageFile!.bytes,
          fileName: selectImageFile!.name
        ) ?? "";
      }

      SeriesResp tempResourceResp;
      if (widget.resp == null) {
        tempResourceResp = SeriesResp();
      } else {
        tempResourceResp = widget.resp!;
      }

      tempResourceResp.seriesRelations = langList
          .map((lang) => SeriesRelation(
                langCode: lang.code,
                title: _titleControllers[lang.code ?? '']?.text,
                description: _descControllers[lang.code ?? '']?.text,
                statement: _statementControllers[lang.code ?? '']?.text,
              ))
          .where((rel) => (rel.title?.isNotEmpty ?? false) &&
                         (rel.description?.isNotEmpty ?? false) &&
                         (rel.statement?.isNotEmpty ?? false))
          .toList();
      tempResourceResp.categoryIds = selectCategoryIds;
      tempResourceResp.cover = imageRemoteUrl;

      await Get.find<SeriesController>().addEntity(tempResourceResp.toJson());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }
}
