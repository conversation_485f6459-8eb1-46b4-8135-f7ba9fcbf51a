import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/user_resp.dart';
import '../controllers/users_controller.dart';

class UsersAddWidget extends StatefulWidget {
  final UserResp? resp;
  const UsersAddWidget({super.key, this.resp});

  @override
  State<UsersAddWidget> createState() => _UsersAddWidgetState();
}

class _UsersAddWidgetState extends State<UsersAddWidget> {
  final TextEditingController nicknameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  
  int selectedRole = 1; // 默认为普通用户
  int selectedStatus = 1; // 默认为启用
  
  final List<Map<String, dynamic>> roleOptions = [
    {'value': 1, 'label': '普通用户'},
    {'value': 2, 'label': '管理员'},
    {'value': 3, 'label': '超级管理员'},
  ];
  
  final List<Map<String, dynamic>> statusOptions = [
    {'value': 1, 'label': '启用'},
    {'value': 0, 'label': '禁用'},
  ];

  @override
  void initState() {
    super.initState();
    if (widget.resp != null) {
      nicknameController.text = widget.resp!.nickname ?? '';
      emailController.text = widget.resp!.email ?? '';
      phoneController.text = widget.resp!.phone ?? '';
      selectedRole = widget.resp!.role ?? 1;
      selectedStatus = widget.resp!.status ?? 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加用户" : "编辑用户"),
      content: SizedBox(
        width: 500,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: nicknameController,
                      decoration: const InputDecoration(
                        labelText: "用户昵称 *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: emailController,
                      decoration: const InputDecoration(
                        labelText: "邮箱",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: phoneController,
                      decoration: const InputDecoration(
                        labelText: "手机号",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: passwordController,
                      decoration: const InputDecoration(
                        labelText: widget.resp == null ? "密码 *" : "密码 (留空不修改)",
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: "用户角色 *",
                        border: OutlineInputBorder(),
                      ),
                      value: selectedRole,
                      items: roleOptions.map((option) {
                        return DropdownMenuItem<int>(
                          value: option['value'],
                          child: Text(option['label']),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedRole = value ?? 1;
                        });
                      },
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: "状态 *",
                        border: OutlineInputBorder(),
                      ),
                      value: selectedStatus,
                      items: statusOptions.map((option) {
                        return DropdownMenuItem<int>(
                          value: option['value'],
                          child: Text(option['label']),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedStatus = value ?? 1;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (nicknameController.text.isEmpty) {
      Get.snackbar("错误", "用户昵称不能为空");
      return false;
    }
    if (widget.resp == null && passwordController.text.isEmpty) {
      Get.snackbar("错误", "密码不能为空");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    Map<String, dynamic> data = {
      'nickname': nicknameController.text,
      'email': emailController.text,
      'phone': phoneController.text,
      'role': selectedRole,
      'status': selectedStatus,
    };

    if (passwordController.text.isNotEmpty) {
      data['password'] = passwordController.text;
    }

    if (widget.resp != null) {
      data['id'] = widget.resp!.id;
    }

    try {
      await Get.find<UsersController>().addEntity(data);
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    nicknameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    passwordController.dispose();
    super.dispose();
  }
}
